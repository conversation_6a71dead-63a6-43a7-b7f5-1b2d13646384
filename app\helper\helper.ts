export const concatenateTitle = (...titles: string[]): string => {
  return titles.filter(Boolean).join('｜')
}

export const flattenRoutes = (routes: any[], parentPath = ''): string[] => {
  const paths: string[] = []

  routes.forEach((route) => {
    const fullPath = `${parentPath}/${route.path || ''}`.replace(/\/+/g, '/')
    paths.push(fullPath)

    if (route.children) {
      paths.push(...flattenRoutes(route.children, fullPath))
    }
  })

  return paths.map((path) => path.replace(/:\w+/g, '\\d+'))
}

export const truncateText = (text: string, maxLength: number = 40) => {
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text
}

// Helper function to create formatted date
export const getFormattedDate = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  return `${year}${month}${day}`
}

export const getMemberLabel = (value: string | number): string => {
  switch (value) {
    case '1':
    case 1:
      return '1日でも在籍'
    case '2':
    case 2:
      return '年度末在籍'
    case '3':
    case 3:
      return '継続在籍'
    default:
      return '1日でも在籍'
  }
}

export const formatNumber = (value: string, isPercent = false) => {
  const num = Number(value)

  if (isNaN(num)) {
    return value
  }

  if (isPercent) {
    const rounded = Math.round(num * 10) / 10
    const parts = rounded.toFixed(1).split('.')
    const intPart = Number(parts[0]).toLocaleString('en-US')
    const decimalPart = parts[1] || '0'
    return `${intPart}.${decimalPart}`
  } else {
    return Math.round(num).toLocaleString('en-US')
  }
}

export const formatYearMonth = (value: string | number): string => {
  const stringValue = String(value)
  if (!stringValue || stringValue.length !== 6) {
    return stringValue
  }

  const year = stringValue.substring(0, 4)
  const month = stringValue.substring(4, 6)

  return `${year}/${month}`
}

export const createRoutesFromConfig = (
  config: any,
  parentPath = '',
): string[] => {
  const routes: string[] = []

  Object.values(config).forEach((route: any) => {
    if (typeof route === 'string') {
      const fullPath = [parentPath, route].join('/').replace(/\/+/g, '/')
      routes.push(fullPath)
    } else if (route && typeof route === 'object') {
      const currentPath =
        route.path ?
          [parentPath, route.path].join('/').replace(/\/+/g, '/')
        : parentPath

      if (route.path) {
        routes.push(currentPath)
      }

      if (route.children) {
        routes.push(...createRoutesFromConfig(route.children, currentPath))
      }
    }
  })

  return routes
}

export const splitCodeAndNumber = (str: string): [string, string] => {
  if (!str) return ["", ""];

  const matches = str.match(/\(\d+\)|\d+/g);

  if (matches && matches.length === 2) {
    return matches as [string, string];
  }

  return [str, ""];
};
