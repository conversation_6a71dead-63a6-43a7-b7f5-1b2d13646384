import HeaderSection from '../../components/common/Header'
import { EventNoteIcon, iconSize } from '../../components/common/Icon'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'

export function meta() {
  return [{ title: title.dataExtractionPeriod.title }]
}

type DataType = {
  id: number
  name: string
  period: string
}

const dataTypes: DataType[] = [
  {
    id: 1,
    name: '台帳データ',
    period: 'yyyy_mm ~ yyyy/mm',
  },
  {
    id: 2,
    name: '健診データ',
    period: 'yyyy_mm ~ yyyy/mm',
  },
  {
    id: 3,
    name: 'レセプトデータ',
    period: 'yyyy_mm ~ yyyy/mm',
  },
  {
    id: 4,
    name: '他組合データ',
    period: 'yyyy_mm ~ yyyy/mm',
  },
]

export default function DataExtractionPeriodPage() {
  return (
    <>
      <HeaderSection
        breadcrumbLinks={[
          { label: title.home.title, to: '/' },
          {
            label: title.dataExtractionPeriod.title,
            to: routeUrl.DATA_EXTRACTION_PERIOD.path,
            isCurrent: true,
          },
        ]}
        firstTitle={title.dataExtractionPeriod.title}
        icon={
          <EventNoteIcon
            fill={''}
            width={iconSize.medium}
            height={iconSize.medium}
          />
        }
      />

      <div className={'mt-6'}>
        <div className={'overflow-x-auto p-4'}>
          <table className={'border-collapse'}>
            <thead>
              <tr className={'h-12 bg-gray-50'}>
                <th
                  className={
                    'w-48 min-w-[200px] border-b border-r border-gray-300 px-4 py-3 text-left text-sm text-gray-900'
                  }
                >
                  対象データ
                </th>
                <th
                  className={
                    'w-[888px] border-b border-gray-300 px-4 py-3 text-left text-sm text-gray-900'
                  }
                >
                  抽出期間
                </th>
              </tr>
            </thead>
            <tbody>
              {dataTypes.map((item) => (
                <tr key={item.id} className={'h-12'}>
                  <td
                    className={
                      'w-48 min-w-[200px] border-b border-r border-gray-300 bg-gray-50 px-4 py-3 text-sm text-gray-900'
                    }
                  >
                    {item.name}
                  </td>
                  <td
                    className={
                      'w-[888px] border-b border-gray-300 px-4 py-3 text-sm text-gray-900'
                    }
                  >
                    {item.period}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </>
  )
}
