import React, { useCallback, useState } from 'react'
import { useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import HeaderSection from '../../components/common/Header'
import { MenuList, iconSize } from '../../components/common/Icon'
import SelectCustom from '../../components/common/SelectCustom'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'
import { useCompanies } from '../../hooks/useCompanies'
import type { RootState } from '../../store/store'
import type { Route } from './+types/regular-report'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [{ title: title.regularReport.title }]
}

const RegularReport: React.FC = () => {
  const [selectedYear, setSelectedYear] = useState<number>(2024)
  const [selectedCompany, setSelectedCompany] = useState<string>('')

  const user = useSelector((state: RootState) => state.auth.user)

  const {
    companies,
    loading: companiesLoading,
    error: companiesError,
  } = useCompanies(user?.hierarchyLevel || 2)

  const handleYearChange = useCallback(
    (value: string | number | (string | number)[]) => {
      if (typeof value === 'number') {
        setSelectedYear(value)
      }
    },
    [],
  )

  const handleCompanyChange = useCallback(
    (value: string | number | (string | number)[]) => {
      if (typeof value === 'string') {
        setSelectedCompany(value)
      }
    },
    [],
  )

  return (
    <>
      <HeaderSection
        breadcrumbLinks={[
          { label: title.home.title, to: '/' },
          {
            label: title.regularReport.title,
            to: routeUrl.REGULAR_REPORT.path,
            isCurrent: true,
          },
        ]}
        firstTitle={title.regularReport.title}
        icon={
          <MenuList
            fill={''}
            width={iconSize.medium}
            height={iconSize.medium}
          />
        }
      />
      <div className={'mt-6 text-base'}>
        <span>
          定期レポートとは組合の特定期間における健康概要を把握することができるレポートです。
        </span>
        <br />
        <span>
          加入者の健診・レセプトデータをもとに組合の状態を確認しましょう。
        </span>

        <div className={'mt-5'}>
          <span className={'mr-1'}>対象年度：</span>{' '}
          <SelectCustom
            labelId={'year-label'}
            defaultLabel={selectedYear.toString() + '年度'}
            defaultValue={selectedYear}
            menuItems={[
              { value: 2024, label: '2024年度' },
              { value: 2023, label: '2023年度' },
              { value: 2022, label: '2022年度' },
              { value: 2021, label: '2021年度' },
              { value: 2020, label: '2020年度' },
              { value: 2019, label: '2019年度' },
              { value: 2018, label: '2018年度' },
              { value: 2017, label: '2017年度' },
              { value: 2016, label: '2016年度' },
              { value: 2015, label: '2015年度' },
              { value: 2014, label: '2014年度' },
              { value: 2013, label: '2013年度' },
              { value: 2012, label: '2012年度' },
            ]}
            width={'112px'}
            height={'40px'}
            isRegularExport={true}
            onChange={handleYearChange}
          />{' '}
          <span className={'ml-1 text-sm'}>
            ※指定された年度のレポートが抽出されます
          </span>
        </div>

        <div className={'mt-6'}>
          <span className={'mr-1'}>対象記号：</span>{' '}
          <SelectCustom
            labelId={'company-label'}
            defaultLabel={
              companiesLoading ? '読み込み中...'
              : companiesError ?
                'エラーが発生しました'
              : '選択してください'
            }
            defaultValue={selectedCompany}
            menuItems={companies}
            width={'383px'}
            height={'40px'}
            isRegularExport={true}
            onChange={handleCompanyChange}
            disabled={companiesLoading || !!companiesError}
          />{' '}
          {companiesError && (
            <div className="mt-2 text-sm text-red-500">
              会社データの取得に失敗しました: {companiesError}
            </div>
          )}
        </div>

        <div className={'mt-[64px] flex justify-center'}>
          <button
            className={`h-[48px] w-[200px] rounded-md px-4 py-2 text-center text-lg-bold
      ${
        selectedCompany && !companiesLoading ?
          'bg-green-600 text-white hover:bg-green-700'
        : 'cursor-not-allowed bg-gray-300 text-white'
      }`}
            disabled={!selectedCompany || companiesLoading}
          >
            抽出する
          </button>
        </div>

        <div className={'mt-4 flex justify-center'}>
          <span>※抽出した定期レポートは、</span>{' '}
          <Link
            to={routeUrl.DOWNLOAD_LIST.path}
            className={
              'mr-1 cursor-pointer text-green-600 underline hover:text-green-700'
            }
          >
            ダウンロード一覧
          </Link>
          <span>から確認することができます。</span>
        </div>
      </div>
    </>
  )
}

export default RegularReport
