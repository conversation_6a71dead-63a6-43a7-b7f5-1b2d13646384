import React from 'react'
import { useTableauPage } from '../../hooks/useTableauPage'
import MemberCountHeader from '../../routes/member-info/MemberInfoHeader'
import TableauWeb from '../tableau/tableauWeb'
import FilterByYear from './FilterByYearWildCard'
import TabSelector from './TabSelector'

export type Tab = {
  label: string
  key: string
  route: string
}

export type FilterByYearProps = {
  notHasAgeGroup?: boolean
  notHasInsurance?: boolean
  hasMember?: boolean
  hasFilterRadio?: boolean
  hasDiabetes?: boolean
  hasCancer?: boolean
  excludeExport?: string[]
}

export type TableauWebProps = {
  src: string
  filterKey?: string[]
  markKey?: string[]
  exportTitle?: string
}

export type TableauPageBaseProps = {
  // Header props
  headerSecondLabel?: string
  showHeader?: boolean
  HeaderComponent?: React.ComponentType<{ secondLabel: string }>

  // Tab props
  tabs?: Tab[]
  showTabs?: boolean

  // FilterByYear props
  filterByYearProps: FilterByYearProps

  // TableauWeb props
  tableauWebProps: TableauWebProps

  additionalComponents?: React.ReactNode[]
}

const TableauPageBase: React.FC<TableauPageBaseProps> = ({
  headerSecondLabel,
  showHeader = false,
  HeaderComponent,
  tabs,
  showTabs = false,
  filterByYearProps,
  tableauWebProps,
  additionalComponents,
}) => {
  const {
    filters,
    tableauFilters,
    exportType,
    exportTrigger,
    setFilters,
    handleFilterChange,
    handleExport,
  } = useTableauPage()

  const HeaderToRender = HeaderComponent || MemberCountHeader

  return (
    <>
      {showHeader && headerSecondLabel && (
        <HeaderToRender secondLabel={headerSecondLabel} />
      )}

      {showTabs && tabs && <TabSelector tabs={tabs} />}

      <FilterByYear
        {...filterByYearProps}
        filters={filters}
        onFilterChange={handleFilterChange}
        onExport={handleExport}
      />

      {additionalComponents && additionalComponents.length > 0 && (
        <div className={'flex justify-center'}>
          {additionalComponents.map((component, index) => (
            <div
              className={'max-w-[1100px]'}
              key={`additional-component-${index}`}
            >
              {component}
            </div>
          ))}
        </div>
      )}

      <TableauWeb
        {...tableauWebProps}
        onFiltersChange={setFilters}
        filters={tableauFilters}
        exportType={exportType}
        exportTrigger={exportTrigger}
      />
    </>
  )
}

export default TableauPageBase
