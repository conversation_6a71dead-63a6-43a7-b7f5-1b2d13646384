import { useEffect, useMemo, useState } from 'react'
import { useSearchParams } from 'react-router'
import FilterByYear from '../../../../components/common/FilterByYearWildCard'
import { Box } from '../../../../components/common/MaterialUI'
import RadioCustom from '../../../../components/common/RadioCustom'
import TableauWeb from '../../../../components/tableau/tableauWeb'
import { EXPORT_TYPES } from '../../../../constants'
import FILTER from '../../../../constants/filter'
import {
  TABLEAU_LINKS,
  TABLEAU_STORAGE_KEYS,
} from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import { useTableauPage } from '../../../../hooks/useTableauPage'
import type { Route } from './+types/classification'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.receiptInformation.title,
        title.receiptInformation.medicalExpense.title,
        title.receiptInformation.medicalExpense.child.receiptClassification,
      ),
    },
  ]
}

const Classification: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const selectedType = parseInt(searchParams.get('selectedType') || '0', 10)
  const [isTableauLoading, setIsTableauLoading] = useState(false)
  const {
    filters,
    tableauFilters,
    exportType,
    exportTrigger,
    setFilters,
    handleFilterChange,
    handleExport,
  } = useTableauPage()

  useEffect(() => {
    const checkLoadingState = () => {
      const loadingState =
        localStorage.getItem(TABLEAU_STORAGE_KEYS.IS_LOADING_TABLEAU) === 'true'
      setIsTableauLoading(loadingState)
    }

    checkLoadingState()

    const interval = setInterval(checkLoadingState, 100)

    return () => clearInterval(interval)
  }, [])

  const handlePeriodTypeChange = (value: number) => {
    localStorage.setItem(TABLEAU_STORAGE_KEYS.IS_LOADING_TABLEAU, 'true')
    handleFilterChange(FILTER.periodParameter, '0')
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev)
      newParams.set('selectedType', value.toString())
      return newParams
    })
  }

  const tableauSource = useMemo(() => {
    return selectedType === 0 ?
        TABLEAU_LINKS.CLASSIFICATION_ALL
      : TABLEAU_LINKS.CLASSIFICATION_PER_MEMBER
  }, [selectedType])

  return (
    <>
      <Box className={'mt-6 flex justify-between gap-4'}>
        <Box className={'ml-2 flex gap-4'}>
          <RadioCustom
            label={'全体'}
            checked={selectedType === 0}
            onChange={() => handlePeriodTypeChange(0)}
            disabled={isTableauLoading}
          />
          <RadioCustom
            label={'加入者一人あたり'}
            checked={selectedType === 1}
            onChange={() => handlePeriodTypeChange(1)}
            disabled={isTableauLoading}
          />
        </Box>
      </Box>

      <FilterByYear
        excludeExport={[EXPORT_TYPES.BID]}
        hasFilterRadio={true}
        filters={filters}
        onFilterChange={handleFilterChange}
        onExport={handleExport}
        selectedType={selectedType}
        hasMember={true}
      />

      <TableauWeb
        src={tableauSource}
        filterKey={[
          FILTER.modal,
          FILTER.yearParameter,
          FILTER.insurance,
          FILTER.member,
        ]}
        markKey={[FILTER.ageGroup]}
        exportTitle={
          title.memberInformation.memberComposition.child.ageGroupComposition
        }
        onFiltersChange={setFilters}
        filters={tableauFilters}
        exportType={exportType}
        exportTrigger={exportTrigger}
      />
    </>
  )
}

export default Classification
