import { useEffect, useState } from 'react'
import { FaBell } from 'react-icons/fa6'
import { Link, Navigate, useParams } from 'react-router'
import HeaderSection from '../../components/common/Header'
import Loading from '../../components/common/Loading'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'
import type { Notice } from '../../services/noticeService'
import { getNoticeById } from '../../services/noticeService'

const NoticeDetail: React.FC = () => {
  const { id } = useParams()
  const [notice, setNotice] = useState<Notice | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchNotice = async () => {
      if (!id) {
        setError('IDが指定されていません')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)
        const noticeData = await getNoticeById(id)
        setNotice(noticeData)
        document.title = noticeData.title
      } catch (err) {
        setError('お知らせの取得に失敗しました')
        console.error('Failed to fetch notice:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchNotice()
  }, [id])

  if (loading) {
    return <Loading />
  }

  if (error || !notice) {
    return <Navigate to={routeUrl.HOME} />
  }

  return (
    <div className={'relative min-h-full'}>
      <div>
        <HeaderSection
          breadcrumbLinks={[
            { label: title.home.title, to: '/' },
            { label: title.notice.title, to: routeUrl.NOTICE.path },
            {
              label: title.notice.detail,
              to: `${routeUrl.NOTICE.path}/${id}`,
              isCurrent: true,
            },
          ]}
          firstTitle={title.notice.title}
          secondTitle={title.notice.detail}
          icon={<FaBell className={'size-6'} />}
        />

        <div className={'mt-6'}>
          <div className={'mb-4 border-b border-dashed pb-2'}>
            <div className={'mb-1 text-xs text-gray-600'}>
              {notice.releaseDate}
            </div>
            <div className={'text-base-bold text-gray-900'}>{notice.title}</div>
          </div>
        </div>

        <div className={'ml-4 mt-10'}>
          <div dangerouslySetInnerHTML={{ __html: notice.comment || '' }} />
          <div dangerouslySetInnerHTML={{ __html: notice.fileName || '' }} />
        </div>
      </div>

      <div
        className={
          'absolute bottom-4 right-0 flex items-end justify-center text-base text-green-600 underline'
        }
        style={{
          left: '1rem',
        }}
      >
        <Link to={'/notices'}>{title.notice.backToList}</Link>
      </div>
    </div>
  )
}

export default NoticeDetail
