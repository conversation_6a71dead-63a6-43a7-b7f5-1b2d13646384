import api from '../core/service/api'

// Interface cho Company data
export interface Company {
  customFieldName: string
  emplrId: string
  emplrName: string
  jigyosyoCd: string
  secondaryUsrProperty: number
}

export interface CompanyResponse {
  emplrs: Company[]
}

// Interface cho SelectCustom option
export interface SelectOption {
  value: string
  label: string
}

export const getCompanies = async (
  hierarchyLevel: number,
): Promise<Company[]> => {
  try {
    const response = await api.get<CompanyResponse>(
      `/api/emplrs/by-hierarchy-role/${hierarchyLevel}`,
    )
    return response.data.emplrs
  } catch (error) {
    console.error('Error fetching companies:', error)
    throw error
  }
}

export const formatCompaniesForSelect = (
  companies: Company[],
): SelectOption[] => {
  return companies.map((company) => ({
    value: company.emplrId,
    label: company.emplrName,
  }))
}

export const getCompaniesForSelect = async (
  hierarchyLevel: number,
): Promise<SelectOption[]> => {
  try {
    const companies = await getCompanies(hierarchyLevel)
    return formatCompaniesForSelect(companies)
  } catch (error) {
    console.error('Error getting companies for select:', error)
    return []
  }
}
