import { FaBell } from 'react-icons/fa'
import { Link, useLocation, useNavigate } from 'react-router'
import HeaderSection from '../../components/common/Header'
import PaginationComponent from '../../components/common/Pagination'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'
import { getNotices } from '../../services/noticeService'
import type { Route } from './+types/notice'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [{ title: title.notice.title }]
}

// Shared header component to avoid duplication
function NoticeHeader() {
  return (
    <HeaderSection
      breadcrumbLinks={[
        { label: title.home.title, to: '/' },
        {
          label: title.notice.title,
          to: routeUrl.NOTICE.path,
          isCurrent: true,
        },
      ]}
      firstTitle={title.notice.title}
      icon={<FaBell className={'size-6'} />}
    />
  )
}

// Client loader function to fetch data
export async function clientLoader({ request }: Route.ClientLoaderArgs) {
  const url = new URL(request.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const limit = 10

  const response = await getNotices(page, limit)
  return {
    notices: response.data,
    total: response.total,
    totalPages: response.totalPages,
    currentPage: page,
    itemsPerPage: limit,
  }
}

export function ErrorBoundary({ error }: Readonly<Route.ErrorBoundaryProps>) {
  return (
    <>
      <NoticeHeader />
      <div className={'mt-6 text-center'}>
        <div className={'mb-4 text-red-600'}>お知らせの取得に失敗しました</div>
        <div className={'text-sm text-gray-500'}>
          {error instanceof Error ? error.message : 'エラーが発生しました'}
        </div>
      </div>
    </>
  )
}

export default function NoticeComponent({
  loaderData,
}: Readonly<Route.ComponentProps>) {
  const location = useLocation()
  const navigate = useNavigate()
  const { notices, total, currentPage, itemsPerPage } = loaderData

  const handlePageChange = (page: number) => {
    const newSearchParams = new URLSearchParams(location.search)
    newSearchParams.set('page', page.toString())
    navigate({ search: newSearchParams.toString() })
  }

  return (
    <>
      <NoticeHeader />

      <div className={'mt-6'}>
        {notices.map((notice) => (
          <Link key={notice.id} to={`${routeUrl.NOTICE.path}/${notice.id}`}>
            <div className={'mb-4 border-b border-dashed pb-2 '}>
              <div className={'mb-1 text-xs text-gray-600'}>
                {notice.releaseDate}
              </div>
              <div className={'text-base text-gray-900 hover:text-green-600'}>
                {notice.title}
              </div>
            </div>
          </Link>
        ))}
      </div>

      {!!notices.length && (
        <PaginationComponent
          totalItems={total}
          itemsPerPage={itemsPerPage}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
      )}
    </>
  )
}
