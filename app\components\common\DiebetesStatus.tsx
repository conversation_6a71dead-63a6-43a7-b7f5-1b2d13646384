import React from 'react'

type HealthMetric = {
  text?: string
  bloodSugar: string
  hba1c: string
  bloodPressure: {
    value: string
    unit: string
  }
  lipids: {
    triglycerides: string
    ldl: string
    hdl: string
  }
}

type HealthStatus = {
  title: string
  subtitle?: string
  description?: string
  backgroundColor: string
  metrics?: HealthMetric
}

type ColumnGroup = {
  title: string
  backgroundColor: string
  textColor: string
  span: number
  columns: HealthStatus[]
}

const columnGroups: ColumnGroup[] = [
  {
    title: '生活習慣病では未通院（未治療）',
    backgroundColor: 'bg-blue-50',
    textColor: 'text-blue-600',
    span: 5,
    columns: [
      {
        title: '未把握',
        subtitle: '（健診未受診）',
        description: '不明',
        metrics: {
          text: '健診データなし\n生活習慣病\nレセプトなし',
          bloodSugar: '',
          hba1c: '',
          bloodPressure: {
            value: '',
            unit: '',
          },
          lipids: {
            triglycerides: '',
            ldl: '',
            hdl: '',
          },
        },
        backgroundColor: 'bg-blueGray-300',
      },
      {
        title: '正常群',
        description: '正常',
        backgroundColor: 'bg-lime-200',
        metrics: {
          bloodSugar: '100mg/dl未満',
          hba1c: '5.6％未満',
          bloodPressure: {
            value: '130/85',
            unit: 'mmHg未満',
          },
          lipids: {
            triglycerides: '150 mg/dl未満',
            ldl: '120 mg/dl未満',
            hdl: '40 mg/dl超過',
          },
        },
      },
      {
        title: '不健康群',
        description: '保健指導域',
        backgroundColor: 'bg-yellow-200',
        metrics: {
          bloodSugar: '100 mg/dl以上',
          hba1c: '5.6 %以上',
          bloodPressure: {
            value: '130又は85',
            unit: 'mmHg以上',
          },
          lipids: {
            triglycerides: '150 mg/dl以上',
            ldl: '120 mg/dl以上',
            hdl: '',
          },
        },
      },
      {
        title: '患者予備群',
        description: '受診勧奨域',
        backgroundColor: 'bg-yellow-400',
        metrics: {
          bloodSugar: '110 mg/dl以上',
          hba1c: '6.0 %以上',
          bloodPressure: {
            value: '140以上又は',
            unit: '90mmHg以上',
          },
          lipids: {
            triglycerides: '300 mg/dl以上',
            ldl: '140 mg/dl以上',
            hdl: '40mg/dl未満',
          },
        },
      },
      {
        title: '治療放置群',
        description: '治療域',
        backgroundColor: 'bg-orange-300',
        metrics: {
          bloodSugar: '126 mg/dl以上',
          hba1c: '6.5 %以上',
          bloodPressure: {
            value: '160又は',
            unit: '100mmHg以上',
          },
          lipids: {
            triglycerides: '500 mg/dl以上',
            ldl: '180 mgdl以上',
            hdl: '35mg/dl未満',
          },
        },
      },
    ],
  },
  {
    title: '生活習慣病通院（治療歴）あり',
    backgroundColor: 'bg-red-50',
    textColor: 'text-red-700',
    span: 3,
    columns: [
      {
        title: '生活習慣病群',
        description: '合併症なし',
        backgroundColor: 'bg-red-100',
        metrics: {
          text: '2型糖尿病・高血圧症・脂質異常症のいずれかがあり、合併症はない状態',
          bloodSugar: '',
          hba1c: '',
          bloodPressure: {
            value: '',
            unit: '',
          },
          lipids: {
            triglycerides: '',
            ldl: '',
            hdl: '',
          },
        },
      },
      {
        title: '重症化群',
        description: '合併症進行',
        backgroundColor: 'bg-red-300',
        metrics: {
          text: '生活習慣病があり、糖尿病性合併症・脳血管疾患・冠動脈疾患・虚血性心疾患がある状態',
          bloodSugar: '',
          hba1c: '',
          bloodPressure: {
            value: '',
            unit: '',
          },
          lipids: {
            triglycerides: '',
            ldl: '',
            hdl: '',
          },
        },
      },
      {
        title: '生活機能低下群',
        description: '重篤な状態',
        backgroundColor: 'bg-red-500',
        metrics: {
          text: '入院を伴う四肢切断急性期・冠動脈疾患急性期・脳卒中急性期、および透析期の状態',
          bloodSugar: '',
          hba1c: '',
          bloodPressure: {
            value: '',
            unit: '',
          },
          lipids: {
            triglycerides: '',
            ldl: '',
            hdl: '',
          },
        },
      },
    ],
  },
]

const renderMetrics = (metrics?: HealthMetric, columnIndex?: number) => {
  if (!metrics) return null

  if (metrics.text) {
    return (
      <div className={'-mt-1 whitespace-pre-line'} style={{ fontSize: '12px' }}>
        {metrics.text}
      </div>
    )
  }

  const showOr = columnIndex !== 1

  return (
    <div style={{ fontSize: '12px' }}>
      <div
        className={'relative -mt-1 mb-2 pb-2'}
        style={{
          borderBottom: '1px dashed white',
          width: 'calc(100% + 16px)',
          marginLeft: '-8px',
          paddingLeft: '8px',
          paddingRight: '8px',
        }}
      >
        【空腹時血糖】
        <br />
        {metrics.bloodSugar}
        <br />
        {showOr ? '又は' : ''}
        <br />
        {columnIndex === 3 ? '【HbA1】' : '【HbA1c】'}
        <br />
        {metrics.hba1c}
      </div>

      <div
        className={'relative mb-2 pb-2'}
        style={{
          borderBottom: '1px dashed white',
          width: 'calc(100% + 16px)',
          marginLeft: '-8px',
          paddingLeft: '8px',
          paddingRight: '8px',
        }}
      >
        【血圧】
        <br />
        {metrics.bloodPressure.value}
        <br />
        {metrics.bloodPressure.unit}
      </div>

      <div>
        【中性脂肪】
        <br />
        {metrics.lipids.triglycerides}
        <br />
        {showOr ? '又は' : ''}
        <br />
        【LDL】
        <br />
        {metrics.lipids.ldl}
        <br />
        {metrics.lipids.hdl && (
          <>
            {showOr ? '又は' : ''}
            <br />
            【HDL】
            <br />
            {metrics.lipids.hdl}
          </>
        )}
      </div>
    </div>
  )
}

const DiabetesStatus: React.FC = () => {
  const allColumns = columnGroups.reduce(
    (acc, group) => [...acc, ...group.columns],
    [] as HealthStatus[],
  )

  return (
    <div className={'mt-8 flex flex-col gap-2'}>
      <div className={'grid grid-cols-8 gap-2'}>
        {columnGroups.map((group, groupIndex) => (
          <div
            key={`group-${groupIndex}`}
            className={`${
              groupIndex === 0 ? 'col-span-5' : 'col-span-3'
            } ${group.backgroundColor} ${group.textColor} flex h-10 items-center justify-center rounded-full text-center font-semibold`}
          >
            {group.title}
          </div>
        ))}
      </div>

      <div className={'grid grid-cols-8 gap-2 overflow-visible'}>
        {allColumns.map((status, index) => (
          <div
            key={`container-${index}`}
            className={'relative -mr-2 ml-0 h-20'}
          >
            <div className={'absolute inset-0'} />
            <div
              className={`${status.backgroundColor} status-box relative flex h-full flex-col items-center justify-center overflow-hidden py-8 text-center text-sm font-semibold text-gray-900`}
            >
              <div className={'font-bold'}>{status.title}</div>
              {status.subtitle && (
                <div className={'text-sm font-bold'}>{status.subtitle}</div>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className={'grid grid-cols-8 gap-3'}>
        {allColumns.map((status, index) => (
          <div
            key={`content-${index}`}
            className={`${status.backgroundColor} p-2 shadow-lg`}
            style={{
              width: '126px',
              height: '336px',
              minWidth: '126px',
              minHeight: '336px',
              maxWidth: '126px',
              maxHeight: '336px',
            }}
          >
            <div className={'text-xs'}>
              {status.description && (
                <div
                  className={'relative mb-3 pb-2 text-center font-semibold'}
                  style={{
                    borderBottom: '1px solid white',
                    width: 'calc(100% + 16px)',
                    marginLeft: '-8px',
                    paddingLeft: '8px',
                    paddingRight: '8px',
                  }}
                >
                  {status.description}
                </div>
              )}
              {renderMetrics(status.metrics, index)}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default DiabetesStatus
