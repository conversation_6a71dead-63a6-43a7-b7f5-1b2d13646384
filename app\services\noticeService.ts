import api from '../core/service/api'

export type Notice = {
  id: string
  title: string
  comment?: string
  fileName?: string
  releaseDate: string
  createdAt?: string
  updatedAt?: string
}

export type Maintain = {
  id: string
  title: string
  comment?: string
  fileName?: string
  releaseDate: string
  createdAt?: string
  updatedAt?: string
}

export const getNotices = async (
  page: number = 1,
  limit: number = 10,
): Promise<{ data: Notice[]; total: number; totalPages: number }> => {
  const response = await api.get(`/api/notices?page=${page}&limit=${limit}`)
  return response.data
}

export const getNoticeById = async (id: string): Promise<Notice> => {
  const response = await api.get(`/api/notice/${id}`)
  return response.data
}

export const getMaintains = async (): Promise<Maintain[]> => {
  const response = await api.get('/api/maintenance-info')
  return response.data.maintenance_info
}
