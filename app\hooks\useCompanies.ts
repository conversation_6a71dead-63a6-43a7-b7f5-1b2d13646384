import { useEffect, useState } from 'react'
import type { SelectOption } from '../services/companyService'
import { getCompaniesForSelect } from '../services/companyService'

export const useCompanies = (hierarchyLevel: number) => {
  const [companies, setCompanies] = useState<SelectOption[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        setLoading(true)
        setError(null)
        const companiesData = await getCompaniesForSelect(hierarchyLevel)
        setCompanies(companiesData)
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to fetch companies',
        )
        console.error('Error in useCompanies:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchCompanies()
  }, [hierarchyLevel])

  return {
    companies,
    loading,
    error,
    refetch: () => {
      const fetchCompanies = async () => {
        try {
          setLoading(true)
          setError(null)
          const companiesData = await getCompaniesForSelect(hierarchyLevel)
          setCompanies(companiesData)
        } catch (err) {
          setError(
            err instanceof Error ? err.message : 'Failed to fetch companies',
          )
        } finally {
          setLoading(false)
        }
      }
      fetchCompanies()
    },
  }
}
