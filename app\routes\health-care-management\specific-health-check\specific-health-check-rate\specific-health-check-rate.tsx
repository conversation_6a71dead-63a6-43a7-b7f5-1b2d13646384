import TableauPageBase from '../../../../components/common/TableauPageBase'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/specific-health-check-rate'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCareManagement.title,
        title.healthCareManagement.specificHealthCheck.title,
        title.healthCareManagement.specificHealthCheck.child
          .specificHealthCheckRate,
      ),
    },
  ]
}

const SpecificHealthCheckRate: React.FC = () => {
  return (
    <>
      <div className={'mt-6 text-sm'}>
        ※ここでは40歳以上の加入者を対象として集計します
      </div>
      <TableauPageBase
        showHeader={true}
        showTabs={true}
        filterByYearProps={{
          hasMember: true,
          hasFilterRadio: true,
        }}
        tableauWebProps={{
          src: TABLEAU_LINKS.SPECIFIC_HEALTH_CHECK_RATE,
        }}
      />
    </>
  )
}

export default SpecificHealthCheckRate
