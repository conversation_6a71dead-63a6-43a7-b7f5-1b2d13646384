import Tooltip, { tooltipClasses } from '@mui/material/Tooltip'
import { memo, useCallback, useEffect, useRef, useState } from 'react'
import FILTER from '../../constants/filter'
import { TABLEAU_STORAGE_KEYS } from '../../constants/tableauLinks'
import TITLE from '../../constants/title'
import { getMemberLabel } from '../../helper/helper'
import variables from '../../theme/variables'
import ExportButton from './ExportButton'
import { InfoCircleIcon } from './Icon'
import { Box } from './MaterialUI'
import RadioCustom from './RadioCustom'
import SelectCustom from './SelectCustom'
import TransferListModal from './TransferList'
import VerticalDivider from './VerticalDivider'

type FilterOption = {
  value: number | string
  label: string
  type: string
  cd: string
  name: string
}

type FilterValue = string | number | (string | number)[]

type FilterByYearProps = {
  hasFilterRadio?: boolean
  notHasAgeGroup?: boolean
  notHasInsurance?: boolean
  notHasYear?: boolean
  hasDisease?: boolean
  hasDiabetes?: boolean
  hasCancer?: boolean
  notHasModal?: boolean
  hasMember?: boolean
  filters?: any[]
  onFilterChange?: (filterName: string, value: any) => void
  onAllAgeGroupSelected?: () => void
  onAllInsuranceSelected?: () => void
  onAllMembersSelected?: () => void
  onAllModalSelected?: () => void
  onExport?: (type: string) => void
  excludeExport?: string[]
  radioChecked?: number
  selectedType?: number
}

const FilterByYear: React.FC<FilterByYearProps> = ({
  hasFilterRadio = false,
  notHasAgeGroup = false,
  notHasInsurance = false,
  notHasYear = false,
  hasDisease = false,
  hasDiabetes = false,
  hasCancer = false,
  notHasModal = false,
  hasMember = false,
  filters = [],
  onFilterChange,
  onAllAgeGroupSelected,
  onAllInsuranceSelected,
  onAllMembersSelected,
  onAllModalSelected,
  onExport,
  excludeExport,
  radioChecked = 0,
  selectedType,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [rightListCount, setRightListCount] = useState(0)

  const [selectedInsurance, setSelectedInsurance] = useState<
    (string | number)[]
  >([])
  const [selectedItemModal, setSelectedItemModal] = useState<
    (string | number)[]
  >([])
  const [selectedAgeGroup, setSelectedAgeGroup] = useState<(string | number)[]>(
    [],
  )
  const [selectedYear, setSelectedYear] = useState<FilterValue>('')
  const [selectedDiabetes, setSelectedDiabetes] = useState<FilterValue>('')
  const [selectedCancer, setSelectedCancer] = useState<FilterValue>('')
  const [selectedDisease, setSelectedDisease] = useState<FilterValue>('')
  const [selectedPeriodType, setSelectedPeriodType] = useState<FilterValue>('')
  const [selectedMember, setSelectedMember] = useState<FilterValue>('')
  const [itemYear, setItemYear] = useState<FilterOption[]>([])
  const [itemAgeGroup, setItemAgeGroup] = useState<FilterOption[]>([])
  const [itemDiabetes, setItemDiabetes] = useState<FilterOption[]>([])
  const [itemCancer, setItemCancer] = useState<FilterOption[]>([])
  const [itemDisease, setItemDisease] = useState<FilterOption[]>([])
  const [itemInsurance, setItemInsurance] = useState<FilterOption[]>([])
  const [itemModal, setItemModal] = useState<FilterOption[]>([])
  const [itemPeriodType, setItemPeriodType] = useState<FilterOption[]>([])
  const [itemMember, setItemMember] = useState<FilterOption[]>([])

  const isInitialSetup = useRef(true)
  const [hasInitialData, setHasInitialData] = useState(false)
  const [ageGroup, setAgeGroup] = useState<string>(FILTER.ageGroup)
  const [isTableauLoading, setIsTableauLoading] = useState(false)

  useEffect(() => {
    const checkTableauLoading = () => {
      const loading =
        localStorage.getItem(TABLEAU_STORAGE_KEYS.IS_LOADING_TABLEAU) === 'true'
      setIsTableauLoading(loading)
    }

    checkTableauLoading()

    const interval = setInterval(checkTableauLoading, 50)

    return () => clearInterval(interval)
  }, [])

  const getSelectedCount = (
    selectedValues: (string | number)[],
    items: FilterOption[],
  ): number => {
    if (!selectedValues.length) return 0
    if (selectedValues.includes(0)) {
      return items.filter((item) => item.value !== 0).length
    }
    return selectedValues.length
  }

  const createFilterOptions = (
    values: (string | number)[],
    labelSuffix: string,
    divider?: boolean,
    filterName?: string,
  ): FilterOption[] => {
    const options: FilterOption[] =
      filterName === FILTER.member ?
        []
      : [{ value: 0, label: 'すべて', cd: '', name: '', type: '' }]

    // Sắp xếp insurance filter theo thứ tự cụ thể
    const sortedValues = filterName === FILTER.insurance ?
      [...values].sort((a, b) => {
        console.log('Insurance filter values:', values)
        console.log('Sorting:', a, 'vs', b)
        const order = ['被保険者 男性', '被保険者 女性', '被扶養者 男性', '被扶養者 女性']
        const indexA = order.indexOf(String(a))
        const indexB = order.indexOf(String(b))

        console.log('IndexA:', indexA, 'IndexB:', indexB)

        // Nếu không tìm thấy trong order, đặt ở cuối
        if (indexA === -1 && indexB === -1) return 0
        if (indexA === -1) return 1
        if (indexB === -1) return -1

        return indexA - indexB
      }) : values

    for (const value of sortedValues) {
      if (value !== '%null%') {
        let label = value
        let type = ''
        let cd = ''
        let name = ''

        if (divider && typeof value === 'string') {
          const parts = String(value).split('|')
          label = `${parts?.[0]}`
          type = parts?.[1]
          const match = label.match(/\(([^)]+)\)(.*)/) || []
          cd = match?.[1] || ''
          name = (match?.[2] || '').trimStart()
        } else if (filterName === FILTER.member) {
          label = getMemberLabel(value)
        }

        options.push({
          value,
          label: `${label}${labelSuffix}`,
          type,
          cd,
          name,
        })
      }
    }

    return options
  }

  const isSelectedAll = (
    selectedValues: (string | number)[],
    domainValues: number[],
  ): boolean => domainValues.every((value) => selectedValues.includes(value))

  const handleAllSelection = useCallback(
    (
      setSelectedFn: (value: (string | number)[]) => void,
      onAllSelectedFn?: () => void,
    ) => {
      setSelectedFn([0])
      if (onAllSelectedFn) {
        onAllSelectedFn()
      }
    },
    [],
  )

  const hasSetupInitialValues = useRef(false)

  useEffect(() => {
    if (!filters?.length) {
      return
    }

    if (!hasSetupInitialValues.current) {
      isInitialSetup.current = true
    }

    const yearFilter = filters.find(
      (filter) => filter.name === FILTER.yearParameter,
    )

    const diabetesFilter = filters.find(
      (filter) => filter.name === FILTER.diabetes,
    )

    const cancerFilter = filters.find((filter) => filter.name === FILTER.cancer)

    const periodTypeFilter = filters.find(
      (filter) => filter.name === FILTER.periodParameter,
    )
    const ageGroupFilter = filters.find(
      (filter) =>
        filter?.filterType === 'categorical' &&
        filter.fieldName.includes('Age Group'),
    )

    const diseaseFilter = filters.find(
      (filter) => filter.fieldName === FILTER.disease,
    )
    const insuranceFilter = filters.find(
      (filter) => filter.fieldName === FILTER.insurance,
    )
    const memberFilter = filters.find(
      (filter) => filter.fieldName === FILTER.member,
    )
    const modalFilter = filters.find(
      (filter) => filter.fieldName === FILTER.modal,
    )

    if (ageGroupFilter?.domain?.values || insuranceFilter?.domain?.values) {
      setHasInitialData(true)
    }

    setAgeGroup(ageGroupFilter?.fieldName)

    const setFilterOptions = (
      filter: {
        domain?: { values?: number[] }
        appliedValues?: Array<{ value: number }>
      },
      setItemFn: (options: FilterOption[]) => void,
      setSelectedFn?: (values: (string | number)[]) => void,
      labelSuffix = '',
      divider?: boolean,
      filterName?: string,
    ) => {
      if (!filter?.domain?.values) return

      const options = createFilterOptions(
        filter.domain.values,
        labelSuffix,
        divider,
        filterName,
      )
      setItemFn(options)
      if (!setSelectedFn) return

      if (!filter.appliedValues?.length) {
        setSelectedFn([0])
        return
      }

      const selectedValues = filter.appliedValues.map((value) => value.value)

      if (isSelectedAll(selectedValues, filter.domain.values)) {
        setSelectedFn([0])
      } else {
        setSelectedFn(selectedValues)
      }
    }

    if (yearFilter?.allowableValues) {
      setItemYear(
        yearFilter.allowableValues?.allowableValues
          ?.reverse()
          .map((item: { value?: string }) => ({
            value: item?.value,
            label: `${item?.value}年度`,
          })),
      )
      setSelectedYear(yearFilter.currentValue.value)
    }

    if (diabetesFilter?.allowableValues) {
      setItemDiabetes(
        diabetesFilter.allowableValues?.allowableValues?.map(
          (item: { value?: string; _formatValue?: string }) => ({
            value: item?.value,
            label: item?.value,
          }),
        ),
      )
      setSelectedDiabetes(diabetesFilter.currentValue.value)
    }

    if (cancerFilter?.allowableValues) {
      setItemCancer(
        cancerFilter.allowableValues?.allowableValues?.map(
          (item: { value?: string; _formatValue?: string }) => ({
            value: item?.value,
            label: item?.value,
          }),
        ),
      )
      setSelectedCancer(cancerFilter.currentValue.value)
    }

    if (periodTypeFilter?.allowableValues) {
      setItemPeriodType(
        periodTypeFilter.allowableValues?.allowableValues?.map(
          (item: { value?: string; _formatValue?: string }) => ({
            value: item?.value,
            label: item?.value,
          }),
        ),
      )
      setSelectedPeriodType(periodTypeFilter.currentValue.value)
    }

    setFilterOptions(ageGroupFilter, setItemAgeGroup, setSelectedAgeGroup)
    setFilterOptions(cancerFilter, setItemCancer)
    setFilterOptions(diseaseFilter, setItemDisease)
    setFilterOptions(insuranceFilter, setItemInsurance, setSelectedInsurance, '', false, FILTER.insurance)
    setFilterOptions(
      memberFilter,
      setItemMember,
      setSelectedMember,
      '',
      false,
      FILTER.member,
    )
    setFilterOptions(modalFilter, setItemModal, setSelectedItemModal, '', true)

    if (modalFilter?.appliedValues?.length) {
      const selectedValues = modalFilter.appliedValues.map(
        (value: { value: number }) => value.value,
      )
      setRightListCount(selectedValues.length)
    } else {
      const selectedValues = modalFilter?.domain?.values?.filter(
        (item: string) => item !== '%null%',
      )
      setRightListCount(selectedValues?.length ?? 0)
    }

    if (!hasSetupInitialValues.current) {
      hasSetupInitialValues.current = true

      setTimeout(() => {
        isInitialSetup.current = false
        setHasInitialData(false)
      }, 1)
    }

    console.log('Filters:', filters)
  }, [
    filters,
    onAllAgeGroupSelected,
    onAllInsuranceSelected,
    onAllMembersSelected,
    onAllModalSelected,
    handleAllSelection,
  ])

  const handleSelectChange = useCallback(
    (
      value: FilterValue,
      setSelectedFn:
        | React.Dispatch<React.SetStateAction<(string | number)[]>>
        | ((value: FilterValue) => void),
      filterName: string,
      isMultiSelect = false,
      onAllSelectedFn?: () => void,
    ) => {
      if (isInitialSetup.current) {
        if (isMultiSelect) {
          const newValue = value as (string | number)[]
          const hasAll = newValue.includes(0)
          const valuesWithoutAll = newValue.filter((v) => v !== 0)

          if (hasAll) {
            handleAllSelection(setSelectedFn, onAllSelectedFn)
          } else if (!valuesWithoutAll.length) {
            setSelectedFn([] as (string | number)[])
          } else {
            setSelectedFn(valuesWithoutAll)
          }
        } else {
          if (typeof setSelectedFn === 'function') {
            if (Array.isArray(value)) {
              setSelectedFn(value)
            } else {
              setSelectedFn([value as string | number])
            }
          }
        }
        return
      }

      if (isMultiSelect) {
        const newValue = value as (string | number)[]
        const hasAll = newValue.includes(0)
        const valuesWithoutAll = newValue.filter((v) => v !== 0)

        if (hasAll) {
          handleAllSelection(setSelectedFn, onAllSelectedFn)
          onFilterChange?.(filterName, 0)
        } else if (!valuesWithoutAll.length) {
          setSelectedFn([] as (string | number)[])
          onFilterChange?.(filterName, [])
        } else {
          setSelectedFn(valuesWithoutAll)
          onFilterChange?.(filterName, valuesWithoutAll)
        }
      } else {
        if (typeof setSelectedFn === 'function') {
          if (Array.isArray(value)) {
            setSelectedFn(value)
          } else {
            setSelectedFn([value as string | number])
          }
        }
        onFilterChange?.(filterName, value)
      }
    },
    [onFilterChange, handleAllSelection, isInitialSetup],
  )

  const handleInputChange = useCallback(
    (
      value: FilterValue,
      setSelectedFn:
        | React.Dispatch<React.SetStateAction<(string | number)[]>>
        | ((value: FilterValue) => void),
      filterName: string,
      isMultiSelect = false,
      onAllSelectedFn?: () => void,
    ) => {
      if (isInitialSetup.current) {
        if (isMultiSelect) {
          const newValue = value as (string | number)[]
          if (typeof setSelectedFn === 'function') {
            setSelectedFn(newValue)
          }
        } else {
          if (typeof setSelectedFn === 'function') {
            if (Array.isArray(value)) {
              setSelectedFn(value)
            } else {
              setSelectedFn([value as string | number])
            }
          }
        }
        return
      }

      return handleSelectChange(
        value,
        setSelectedFn,
        filterName,
        isMultiSelect,
        onAllSelectedFn,
      )
    },
    [handleSelectChange, isInitialSetup],
  )

  const handleOpenModal = useCallback(() => {
    setIsModalOpen(true)
  }, [])

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false)
  }, [])

  useEffect(() => {
    setSelectedPeriodType(['Year'])
  }, [selectedType])

  const isChecked = (index: number) => {
    return (
      selectedPeriodType === itemPeriodType[index]?.value ||
      (Array.isArray(selectedPeriodType) &&
        selectedPeriodType[0] === itemPeriodType[index]?.value)
    )
  }

  return (
    <>
      <Box className={'mt-6 flex items-center gap-4'}>
        {!notHasModal && (
          <>
            <button
              className={`h-[28px] w-[140px] rounded-lg border border-gray-300 text-sm ${isTableauLoading ? 'cursor-not-allowed opacity-50' : 'hover:border-black'}`}
              onClick={handleOpenModal}
              disabled={isTableauLoading}
            >
              記号・所属{' '}
              <span className={'text-xxs'}>
                {rightListCount > 0 ? `(${rightListCount}件)` : ''}
              </span>
            </button>
            <VerticalDivider />
          </>
        )}

        {!notHasYear && (
          <SelectCustom
            labelId={'year-label'}
            defaultValue={selectedYear}
            defaultLabel={selectedYear ? `${selectedYear}年度` : '2025年度'}
            menuItems={itemYear}
            width={'106px'}
            onChange={(value) =>
              handleInputChange(value, setSelectedYear, FILTER.yearParameter)
            }
            disabled={isTableauLoading}
          />
        )}

        {hasFilterRadio && (
          <Box className={'flex items-center gap-4'}>
            <RadioCustom
              size={'small'}
              label={TITLE.filterByYear.year}
              checked={isChecked(radioChecked)}
              onChange={() =>
                handleInputChange(
                  itemPeriodType[0]?.value as string,
                  setSelectedPeriodType,
                  FILTER.periodParameter,
                )
              }
              disabled={isTableauLoading}
            />
            <RadioCustom
              size={'small'}
              label={TITLE.filterByYear.month}
              checked={isChecked(1)}
              onChange={() =>
                handleInputChange(
                  itemPeriodType[1]?.value as string,
                  setSelectedPeriodType,
                  FILTER.periodParameter,
                )
              }
              disabled={isTableauLoading}
            />
          </Box>
        )}

        {!notHasAgeGroup && (
          <>
            <VerticalDivider />
            <SelectCustom
              key={hasInitialData ? `${selectedAgeGroup.join(',')}` : undefined}
              labelId={'age-label'}
              defaultValue={selectedAgeGroup}
              defaultLabel={`年齢層 `}
              countLabel={
                getSelectedCount(selectedAgeGroup, itemAgeGroup) > 0 ?
                  `(${getSelectedCount(selectedAgeGroup, itemAgeGroup)}件)`
                : ''
              }
              menuItems={itemAgeGroup}
              width={'126px'}
              multiple={true}
              onChange={(value) =>
                handleInputChange(
                  value,
                  setSelectedAgeGroup,
                  ageGroup,
                  true,
                  isInitialSetup.current ? onAllAgeGroupSelected : undefined,
                )
              }
              disabled={isTableauLoading}
            />
          </>
        )}

        {hasDiabetes && (
          <>
            <VerticalDivider />
            <SelectCustom
              labelId={'diabetes-label'}
              defaultValue={selectedDiabetes}
              defaultLabel={selectedDiabetes ? selectedDiabetes : '2型糖尿病'}
              menuItems={itemDiabetes}
              width={'166px'}
              onChange={(value) =>
                handleInputChange(value, setSelectedDiabetes, FILTER.diabetes)
              }
              disabled={isTableauLoading}
            />
          </>
        )}

        {hasCancer && (
          <>
            <VerticalDivider />
            <SelectCustom
              labelId={'cancer-label'}
              defaultValue={selectedCancer}
              defaultLabel={selectedCancer ? selectedCancer : '胃がん'}
              menuItems={itemCancer}
              width={'166px'}
              onChange={(value) =>
                handleInputChange(value, setSelectedCancer, FILTER.cancer)
              }
              disabled={isTableauLoading}
            />
          </>
        )}

        {!notHasInsurance && (
          <>
            <VerticalDivider />
            <SelectCustom
              key={
                hasInitialData ? `${selectedInsurance.join(',')}` : undefined
              }
              labelId={'insurance-label'}
              defaultValue={selectedInsurance}
              defaultLabel={'加入者区分'}
              countLabel={
                getSelectedCount(selectedInsurance, itemInsurance) > 0 ?
                  `(${getSelectedCount(selectedInsurance, itemInsurance)}件)`
                : ''
              }
              menuItems={itemInsurance}
              width={'147px'}
              multiple={true}
              onChange={(value) =>
                handleInputChange(
                  value,
                  setSelectedInsurance,
                  FILTER.insurance,
                  true,
                  isInitialSetup.current ? onAllInsuranceSelected : undefined,
                )
              }
              disabled={isTableauLoading}
            />
          </>
        )}

        {hasDisease && (
          <SelectCustom
            labelId={'disease-label'}
            defaultValue={selectedDisease}
            defaultLabel={'疾病'}
            menuItems={itemDisease}
            width={'106px'}
            onChange={(value) =>
              handleInputChange(value, setSelectedDisease, FILTER.disease)
            }
            disabled={isTableauLoading}
          />
        )}

        {hasMember && (
          <>
            <VerticalDivider />
            <SelectCustom
              labelId={'member-label'}
              defaultValue={selectedMember}
              defaultLabel={'1日でも在籍'}
              menuItems={itemMember}
              width={'124px'}
              onChange={(value) =>
                handleInputChange(value, setSelectedMember, FILTER.member)
              }
              disabled={isTableauLoading}
            />
          </>
        )}

        <Box
          style={{
            marginLeft: -10,
            marginRight: 0,
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
          }}
        >
          <Tooltip
            placement={'top-start'}
            slotProps={{
              popper: {
                sx: {
                  [`& .${tooltipClasses.tooltip}`]: {
                    padding: '8px 12px',
                    lineHeight: '20px',
                    marginLeft: '-15px',
                  },
                  [`& .${tooltipClasses.arrow}`]: {
                    marginLeft: '8px',
                  },
                  [`&.${tooltipClasses.popper}[data-popper-placement*="top"] .${tooltipClasses.tooltip}`]:
                    {
                      marginBottom: '8px',
                    },
                },
              },
            }}
            title={
              <>
                1日でも在籍：指定年度に1日でも在籍
                <br />
                年度末在籍：指定年度の年度末に在籍
                <br />
                継続在籍：指定年度に12ヶ月在籍
              </>
            }
            arrow={true}
          >
            <span>
              <InfoCircleIcon fill={variables.gray600} />
            </span>
          </Tooltip>
        </Box>

        <Box className={'ml-auto flex'}>
          <ExportButton
            disabled={isTableauLoading}
            onExport={onExport}
            excludeExport={excludeExport}
          />
        </Box>
      </Box>

      <TransferListModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onRightListChange={setRightListCount}
        menuItems={itemModal}
        selectedList={selectedItemModal}
        onFilterChange={onFilterChange}
      />
    </>
  )
}

export default memo(FilterByYear)
