import { useAuth0 } from '@auth0/auth0-react'
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight'
import Grid from '@mui/material/Grid'
import { useSelector } from 'react-redux'
import { Link, useNavigate } from 'react-router'
import {
  Download,
  ExpandCircleRight,
  GroupSearch,
  HomeIcon,
  MenuList,
  QuickReference,
  ReadinessScore,
  StethoscopeSearch,
  SupportAgent,
} from '../../components/common/Icon'
import Loading from '../../components/common/Loading'
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
} from '../../components/common/MaterialUI'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'
import { getNotices } from '../../services/noticeService'
import type { RootState } from '../../store/store'
import variables from '../../theme/variables'
import type { Route } from './+types/home'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [{ title: title.home.title }]
}

// Client loader function to fetch latest notice
export async function clientLoader() {
  try {
    const response = await getNotices(1, 1)
    return {
      latestNotice: response.data.length ? response.data[0] : null,
    }
  } catch (error) {
    console.error('Failed to fetch latest notice:', error)
    return { latestNotice: null }
  }
}

export default function Home({ loaderData }: Route.ComponentProps) {
  const { isLoading, isAuthenticated } = useAuth0()
  const currentUser = useSelector((state: RootState) => state.auth.user)
  const navigate = useNavigate()
  const { latestNotice } = loaderData

  if (isLoading || !isAuthenticated || !currentUser) {
    return <Loading />
  }
  const cardData = [
    {
      id: 2,
      title: title.receiptInformation.title,
      description: title.receiptInformation.description,
      icon: <QuickReference />,
      route: routeUrl.RECEIPT.path,
    },
    {
      id: 3,
      title: title.healthCheckForm.title,
      description: title.healthCheckForm.description,
      icon: <StethoscopeSearch />,
      route: routeUrl.HEALTH_CHECK_FORM.path,
    },
    {
      id: 4,
      title: title.memberInformation.title,
      description: title.memberInformation.description,
      icon: <GroupSearch />,
      route: routeUrl.MEMBER_INFO.path,
    },
  ]

  const cardDataBottom = [
    {
      id: 5,
      title: title.healthCareManagement.title,
      description: title.healthCareManagement.description,
      icon: <ReadinessScore />,
      route: routeUrl.HEALTH_CARE_MANAGEMENT.path,
      isHasSubString: true,
    },
    {
      id: 6,
      title: title.regularReport.title,
      description: title.regularReport.description,
      icon: <MenuList />,
      route: routeUrl.REGULAR_REPORT.path,
    },
    {
      id: 7,
      title: title.downloadList.title,
      description: title.downloadList.description,
      icon: <Download />,
      route: routeUrl.DOWNLOAD_LIST.path,
    },
  ]

  const items = [
    title.dataExtractionPeriod,
    title.manual,
    title.variousSettings,
  ]

  const handleCardClick = (route: string) => {
    navigate(route)
  }

  return (
    <div>
      <p className={'flex items-center gap-2 text-xl font-semibold '}>
        <HomeIcon /> <span>{title.home.title}</span>
      </p>
      <div className={'mt-8'}>
        <p className={'font-semibold '}>{title.notice.title}</p>
        {latestNotice ?
          <div
            className={
              'mt-2 grid cursor-pointer grid-cols-12 gap-4 rounded-lg border border-gray-300 p-3 shadow-md'
            }
          >
            <div className={'col-span-10 flex'}>
              <div className={' text-gray-600'}>{latestNotice.releaseDate}</div>
              <div className={'ml-4 hover:text-[#068667] hover:underline'}>
                <Link to={`/notices/${latestNotice.id}`}>
                  {latestNotice.title}
                </Link>
              </div>
            </div>
            <div
              className={
                'col-span-2 flex cursor-pointer items-center justify-end text-green-600 underline hover:text-green-800'
              }
            >
              <Link to={routeUrl.NOTICE.path}>
                {title.home.more}
                <KeyboardArrowRightIcon />
              </Link>
            </div>
          </div>
        : <div
            className={'mt-2 rounded-lg border border-gray-300 p-3 shadow-md'}
          >
            <div className={'text-center text-gray-600'}>
              お知らせはありません
            </div>
          </div>
        }
      </div>
      <Box className={'mt-8 rounded-lg bg-green-50 px-[30px] pb-10 pt-[37px] '}>
        <Grid
          className={'bg-green-50'}
          display={'flex'}
          justifyContent={'space-between'}
        >
          {cardData.map((card) => (
            <Card
              key={card.id}
              component={'button'}
              onClick={() => handleCardClick(card.route)}
              sx={{
                border: `2px solid ${variables.white50}`,
                height: '100%',
                width: '32.3%',
                borderRadius: '12px',
                padding: '1px 0px',
                cursor: 'pointer',
                '&:hover': {
                  color: `${variables.drawerBg}`,
                  border: `2px solid ${variables.drawerBg}`,
                },
                display: 'block',
                textAlign: 'left',
              }}
            >
              <CardContent sx={{ textAlign: 'center', padding: '' }}>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <div className={'mr-2'}>{card.icon}</div>
                  <Typography
                    sx={{ fontSize: '24px', fontWeight: '600' }}
                    variant={'h6'}
                  >
                    {card.title}
                  </Typography>
                </Box>
                <Typography
                  sx={{
                    marginTop: '12px',
                    fontSize: '16px',
                    fontWeight: '400',
                    color: variables.gray900,
                  }}
                  variant={'body2'}
                  color={'text.secondary'}
                >
                  {card.description}
                  <span className={'text-sm text-gray-900'}> など</span>
                </Typography>
              </CardContent>
            </Card>
          ))}
        </Grid>
        <div className={'my-8 border-t border-dashed border-gray-300'} />
        <Grid display={'flex'} justifyContent={'space-between'}>
          {cardDataBottom.map((card) => (
            <Card
              key={card.id}
              component={'button'}
              onClick={() => handleCardClick(card.route)}
              sx={{
                border: `2px solid ${variables.white50}`,
                height: '80px',
                width: '32.3%',
                borderRadius: '12px',
                padding: '0px 12px !important',
                cursor: 'pointer',
                '&:hover': {
                  color: `${variables.drawerBg}`,
                  border: `2px solid ${variables.drawerBg}`,
                },
                display: 'block',
                textAlign: 'left',
              }}
            >
              <CardContent
                sx={{ textAlign: 'center', padding: '8px 12px !important' }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <div className={'mr-2'}>{card.icon}</div>
                  <Typography
                    sx={{ fontSize: '24px', fontWeight: '600' }}
                    variant={'h6'}
                  />
                  <Typography
                    sx={{ fontSize: '20px', fontWeight: '600' }}
                    variant={'h6'}
                  >
                    {card.title}
                  </Typography>
                </Box>
                <Typography
                  sx={{
                    marginTop: '8px',
                    fontSize: '16px',
                    fontWeight: '400',
                    color: variables.gray900,
                  }}
                  variant={'body2'}
                  color={'text.secondary'}
                >
                  {card.description}
                  {card.isHasSubString && (
                    <span className={'text-sm text-gray-900'}> など</span>
                  )}
                </Typography>
              </CardContent>
            </Card>
          ))}
        </Grid>
        <div className={'my-8 border-t border-dashed border-gray-300'} />
        <Box display={'flex'} gap={2} sx={{ marginX: '2px' }}>
          {items.map((text, index) => (
            <Box
              key={index + 1}
              display={'flex'}
              alignItems={'center'}
              gap={0.5}
            >
              <Typography
                sx={{
                  '&:hover': {
                    color: variables.green700,
                    cursor: 'pointer',
                  },
                }}
                fontWeight={'600'}
              >
                {text.title}
              </Typography>
              <ExpandCircleRight />
            </Box>
          ))}
        </Box>
      </Box>
      <Button
        variant={'outlined'}
        sx={{
          position: 'fixed',
          bottom: 65,
          right: 40,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          borderRadius: '50px',
          borderColor: variables.green600,
          height: '84px',
          width: '172px',
          textTransform: 'none',
          border: `3px solid ${variables.green600}`,
          color: variables.green600,
          backgroundColor: 'white',
          boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.1)',
          '&:hover': {
            border: `3px solid ${variables.green700}`,
            '& .MuiSvgIcon-root, & div': {
              color: variables.green700,
            },
          },
        }}
      >
        <Box display={'flex'} alignItems={'center'}>
          <SupportAgent />
          <div className={'ml-1 text-base-bold text-green-600 hover:underline'}>
            {title.contact.subTitle}
          </div>
        </Box>
        <div className={'text-sm !text-gray-900'}>
          {title.contact.description}
        </div>
      </Button>
    </div>
  )
}
