import Tooltip from '@mui/material/Tooltip'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { truncateText } from '../../helper/helper'
import variables from '../../theme/variables'
import { iconSize, KeyboardArrowDown, KeyboardArrowUp } from './Icon'
import { ListItemText, MenuItem, Select } from './MaterialUI'

export type MenuItem = {
  value: string | number
  label: string
}

export type SelectCustomProps = {
  labelId: string
  defaultValue: string | number | (string | number)[]
  menuItems: MenuItem[]
  height?: string
  width?: string
  className?: string
  onClick?: () => void
  defaultLabel?: string | number | (string | number)[]
  countLabel?: string
  multiple?: boolean
  onChange?: (
    value: string | number | (string | number)[],
    skipFilterTrigger?: boolean,
  ) => void
  skipFilterTrigger?: boolean
  disabled?: boolean
  isRegularExport?: boolean
}

const ALL_OPTION_VALUE = 0

const getAllValues = (menuItems: MenuItem[]): (string | number)[] =>
  menuItems.map((item) => item.value)

const getValuesWithoutAll = (
  values: (string | number)[],
): (string | number)[] => values.filter((v) => v !== ALL_OPTION_VALUE)

const areAllItemsSelected = (
  values: (string | number)[],
  menuItems: MenuItem[],
): boolean => {
  const itemsWithoutAll = menuItems
    .filter((item) => item.value !== ALL_OPTION_VALUE)
    .map((item) => item.value)
  return itemsWithoutAll.every((item) => values.includes(item))
}

const SelectCustom: React.FC<SelectCustomProps> = ({
  labelId,
  defaultValue,
  menuItems,
  height = '28px',
  width = '151px',
  className,
  onClick,
  defaultLabel,
  countLabel,
  multiple = false,
  onChange,
  skipFilterTrigger = false,
  disabled = false,
  isRegularExport = false,
}) => {
  const [selectedValues, setSelectedValues] = useState<(string | number)[]>(
    multiple ?
      Array.isArray(defaultValue) ?
        defaultValue
      : []
    : [],
  )
  const [open, setOpen] = useState(false)

  const isHandlingSelectAll = useRef(false)
  const selectRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const selectElement = selectRef.current
      const menuElement = document.querySelector('.MuiMenu-paper')

      if (
        selectElement &&
        !selectElement.contains(event.target as Node) &&
        menuElement &&
        !menuElement.contains(event.target as Node)
      ) {
        setOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleOpen = useCallback(() => {
    setOpen(true)
  }, [])

  const handleClose = useCallback(() => {
    setOpen(false)
  }, [])

  const handleSelectAll = useCallback(
    (_: (string | number)[]) => {
      if (isHandlingSelectAll.current) {
        return
      }

      isHandlingSelectAll.current = true
      const allValues = getAllValues(menuItems)

      const currentValues = new Set(selectedValues)
      const hasChanged =
        allValues.length !== selectedValues.length ||
        allValues.some((value) => !currentValues.has(value))

      if (hasChanged) {
        setSelectedValues(allValues)

        if (onChange) {
          onChange(allValues, skipFilterTrigger)
        }
      }

      setTimeout(() => {
        isHandlingSelectAll.current = false
      }, 0)
    },
    [menuItems, onChange, skipFilterTrigger, selectedValues],
  )

  const handleDeselectAll = useCallback(() => {
    setSelectedValues([])
    if (onChange) {
      onChange([], skipFilterTrigger)
    }
  }, [onChange, skipFilterTrigger])

  const handleNormalSelection = useCallback(
    (values: (string | number)[]) => {
      const valuesWithoutAll = getValuesWithoutAll(values)

      if (
        selectedValues.includes(ALL_OPTION_VALUE) &&
        values.length < selectedValues.length
      ) {
        setSelectedValues(valuesWithoutAll)
        if (onChange) {
          onChange(valuesWithoutAll, skipFilterTrigger)
        }
      } else {
        const allItemsSelected = areAllItemsSelected(values, menuItems)
        const newValues =
          allItemsSelected ? [ALL_OPTION_VALUE, ...values] : values

        setSelectedValues(newValues)
        if (onChange) {
          onChange(newValues, skipFilterTrigger)
        }
      }
    },
    [selectedValues, menuItems, onChange, skipFilterTrigger],
  )

  const handleChange = useCallback(
    (event: any) => {
      const value = event.target.value

      if (multiple) {
        if (
          value.includes(ALL_OPTION_VALUE) &&
          !selectedValues.includes(ALL_OPTION_VALUE)
        ) {
          handleSelectAll(value)
        } else if (
          !value.includes(ALL_OPTION_VALUE) &&
          selectedValues.includes(ALL_OPTION_VALUE)
        ) {
          handleDeselectAll()
        } else {
          handleNormalSelection(value)
        }
      } else {
        if (onChange) {
          onChange(value, skipFilterTrigger)
        }
      }
    },
    [
      multiple,
      selectedValues,
      handleSelectAll,
      handleDeselectAll,
      handleNormalSelection,
      onChange,
      skipFilterTrigger,
      handleClose,
    ],
  )

  const prevSelectedValuesRef = useRef<(string | number)[]>([])

  useEffect(() => {
    if (
      multiple &&
      selectedValues.includes(ALL_OPTION_VALUE) &&
      !isHandlingSelectAll.current &&
      JSON.stringify(prevSelectedValuesRef.current) !==
        JSON.stringify(selectedValues)
    ) {
      if (!prevSelectedValuesRef.current.includes(ALL_OPTION_VALUE)) {
        prevSelectedValuesRef.current = [...selectedValues]
        handleSelectAll(selectedValues)
      }
    } else {
      prevSelectedValuesRef.current = [...selectedValues]
    }
  }, [selectedValues, multiple, handleSelectAll])

  const renderValue = useCallback(
    (selected: any) => {
      const renderContent = (label: string | undefined) => {
        const displayLabel = label || ''
        const shouldShowTooltip = displayLabel.length > 6

        const labelElement =
          shouldShowTooltip ?
            <Tooltip
              title={displayLabel}
              placement={'top'}
              arrow={true}
              slotProps={{
                transition: {
                  timeout: 0,
                },
              }}
            >
              <span>{displayLabel}</span>
            </Tooltip>
          : <span>{displayLabel}</span>

        return (
          <span
            className={`flex items-baseline ${isRegularExport ? 'w-full justify-between text-base' : 'gap-1 text-sm'}`}
          >
            <span className={'flex items-baseline'}>{labelElement}</span>
            <span className={'flex items-baseline justify-center text-xxs'}>
              {countLabel}
            </span>
          </span>
        )
      }

      if (multiple) {
        return renderContent(defaultLabel?.toString() || '')
      }

      if (!selected && defaultLabel) {
        return renderContent(defaultLabel.toString())
      }

      return renderContent(
        menuItems.find((item) => item?.value === selected[0])?.label ||
          defaultLabel?.toString(),
      )
    },
    [multiple, defaultLabel, menuItems, isRegularExport, countLabel],
  )

  const selectStyles = {
    borderRadius: '6px',
    height,
    width,
    '& .MuiSelect-select': {
      color: variables.gray900,
      display: 'flex !important',
      alignItems: 'center !important',
      justifyContent: 'flex-start !important',
      padding: '0px 8px 0px 8px !important',
      height: '100%',
      width: '100%',
    },
    '& .MuiOutlinedInput-notchedOutline': {
      border: '1px solid',
      borderColor: variables.gray300,
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      border: '1px solid',
      borderColor: variables.gray300,
    },
    '&.Mui-focused': {
      backgroundColor: open ? variables.gray50 : 'white',
      boxShadow: 'none',
    },
  }

  return (
    <Select
      ref={selectRef}
      displayEmpty={true}
      inputProps={{ 'aria-label': 'Without label' }}
      sx={selectStyles}
      labelId={labelId}
      value={multiple ? selectedValues : defaultValue}
      className={className}
      onClick={onClick}
      onChange={handleChange}
      onOpen={handleOpen}
      onClose={handleClose}
      open={open}
      multiple={multiple}
      renderValue={renderValue}
      disabled={disabled}
      IconComponent={() => {
        const Icon = open ? KeyboardArrowUp : KeyboardArrowDown
        return (
          <button
            onClick={(e) => {
              e.stopPropagation()
              setOpen(!open)
            }}
            style={{
              marginRight: '8px',
              cursor: 'pointer',
              background: 'none',
              border: 'none',
              padding: 0,
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Icon
              fill={disabled ? variables.gray300 : variables.gray900}
              width={isRegularExport ? iconSize.medium : iconSize.smallMedium}
              height={isRegularExport ? iconSize.medium : iconSize.smallMedium}
            />
          </button>
        )
      }}
      MenuProps={{
        autoFocus: false,
        PaperProps: {
          sx: {
            padding: '5px',
            '&::-webkit-scrollbar': {
              width: isRegularExport ? '12px' : '4px',
              display: 'none',
            },
            '&::-webkit-scrollbar-track': {
              marginY: '8px',
            },
            ...(isRegularExport && {
              marginTop: '-4px',
            }),
            transition: 'none !important',
          },
        },
        MenuListProps: {
          sx: {
            overflowY: 'auto',
            maxHeight: '400px',
          },
        },
      }}
    >
      {menuItems.map((item) => (
        <MenuItem
          key={item.value}
          value={item.value}
          sx={{
            padding: '8px 8px 8px 8px !important',
            margin: '0px 4px !important',
            '&:hover': {
              backgroundColor: `${variables.gray50} !important`,
            },
            '&.Mui-selected': {
              backgroundColor:
                !multiple ?
                  `${variables.gray100} !important`
                : 'inherit !important',
              padding: '8px 8px 8px 8px !important',
              '&:hover': {
                backgroundColor: `${variables.gray100} !important`,
              },
            },
          }}
        >
          {multiple && (
            <input
              type={'checkbox'}
              className={'accent-green-600'}
              checked={selectedValues.includes(item.value)}
              onChange={() => {}}
              style={{ marginRight: '8px' }}
            />
          )}
          <ListItemText
            primary={
              typeof item.value === 'string' && item.value.length > 9 ?
                <Tooltip title={item.value} placement={'right'} arrow={true}>
                  <span className={'text-sm'}>
                    {truncateText(item.value, 9)}
                  </span>
                </Tooltip>
              : <span className={'text-sm'}>{item.label}</span>
            }
          />
        </MenuItem>
      ))}
    </Select>
  )
}

export default SelectCustom
