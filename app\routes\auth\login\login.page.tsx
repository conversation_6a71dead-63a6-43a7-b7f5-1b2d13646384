import { useAuth0 } from '@auth0/auth0-react'
import Button from '../../../components/common/Button'
import { AppConfig } from '../../../configs/appConfig'
import title from '../../../constants/title'
import type { Maintain } from '../../../services/noticeService'
import { getMaintains } from '../../../services/noticeService'
import type { Route } from './+types/login.page'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [{ title: title.login.loginTitle }]
}

// Client loader function to fetch data
export async function clientLoader(): Promise<{ maintains: Maintain[] }> {
  try {
    const maintains = await getMaintains()
    return { maintains }
  } catch (error) {
    console.error('Failed to fetch maintains:', error)
    return { maintains: [] }
  }
}

export default function Login({ loaderData }: Route.ComponentProps) {
  const { loginWithRedirect } = useAuth0()
  const { maintains } = loaderData

  return (
    <div className={'flex h-full flex-col overflow-hidden bg-beige-400'}>
      <div
        className={
          'mx-auto mt-[73px] flex h-[120px] w-[400px] items-center justify-center overflow-hidden'
        }
      >
        <img
          src={'/maintain-page/images/logo-login.png'}
          alt={'Quick Reference'}
        />
      </div>

      {Array.isArray(maintains) && !!maintains.length && (
        <div className={'mx-auto mt-[64px] w-[880px]'}>
          <p className={'text-base font-semibold text-gray-900'}>
            {title.login.maintainTitle}
          </p>
          <div
            className={'mt-2 rounded-xl border border-gray-300 bg-white p-4'}
          >
            <div
              className={
                'flex h-[240px] w-full flex-col gap-4 overflow-y-auto pr-4'
              }
            >
              {maintains.map((maintain, index) => (
                <div key={maintain.id}>
                  <p className={'text-xs text-gray-600'}>
                    {maintain.releaseDate}
                  </p>
                  <p className={'text-sm font-semibold text-gray-900'}>
                    {maintain.title}
                  </p>
                  {maintain.comment && (
                    <p className={'text-sm text-gray-900'}>
                      {maintain.comment}
                    </p>
                  )}
                  {index < maintains.length - 1 && (
                    <hr
                      className={'mt-4 border-t border-dotted border-gray-300'}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <div className={'mt-[64px] flex flex-col items-center h-sm:mt-[2%]'}>
        {AppConfig.maintenanceMode ?
          <p className={'text-lg-bold text-red-700'}>
            {title.login.maintainDesc}
          </p>
        : <Button onClick={loginWithRedirect}>{title.button.login}</Button>}
      </div>
    </div>
  )
}
