import clsx from 'clsx'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { FixedSizeList as FixedSizeListOriginal } from 'react-window'
import FILTER from '../../constants/filter'
import TITLE from '../../constants/title'
import { splitCodeAndNumber, truncateText } from '../../helper/helper'
import '../../style/transferList.css'
import variables from '../../theme/variables'
import { CancelIcon, CloseIcon } from './Icon'
import RadioCustom from './RadioCustom'

const FixedSizeList = FixedSizeListOriginal as any

type TransferListItem = {
  value: number | string
  label: string
  cd: string
  name: string
  type: string
}

type ItemGroupName = '記号' | '所属1' | '所属2' | '所属3' | '所属4'

type GroupState = {
  allItems: TransferListItem[]
  selectedItems: (number | string)[]
  submittedAt: number
}

type InMemoryGroupStates = Partial<Record<ItemGroupName, GroupState>>

type GroupedItemsMap = Record<ItemGroupName, TransferListItem[]>

type SubmittedFilterState = {
  filterCD: any
  filterName: any
  filterType: any
  selectedGroup: ItemGroupName
}

type TransferListModalProps = {
  isOpen: boolean
  onClose: () => void
  onRightListChange?: (count: number) => void
  menuItems?: TransferListItem[]
  selectedList?: (number | string)[]
  onFilterChange?: (fieldName: string, value: any) => void
}

const GROUPS = {
  KIGO: '記号' as ItemGroupName,
  SHOZOKU1: '所属1' as ItemGroupName,
  SHOZOKU2: '所属2' as ItemGroupName,
  SHOZOKU3: '所属3' as ItemGroupName,
  SHOZOKU4: '所属4' as ItemGroupName,
} as const

const GROUP_NAMES = Object.values(GROUPS)

const getItemGroupName = (itemType: string | null): ItemGroupName => {
  if (!itemType) return GROUPS.KIGO

  const nValue = parseInt(itemType, 10)
  return nValue >= 1 && nValue <= 4 ?
      (`所属${nValue}` as ItemGroupName)
    : GROUPS.KIGO
}

const calculateFilterValues = (
  selectedItems: TransferListItem[],
  isAllSelected: boolean,
) => {
  if (isAllSelected) {
    return { filterCD: 0, filterName: 0 }
  }

  const uniqueValues = selectedItems.reduce(
    (acc, item) => {
      acc.cd.add(item.cd)
      acc.name.add(item.name)
      return acc
    },
    { cd: new Set<string>(), name: new Set<string>() },
  )

  return {
    filterCD: Array.from(uniqueValues.cd),
    filterName: Array.from(uniqueValues.name),
  }
}

const calculateFilterType = (groupName: ItemGroupName): number[] => {
  return [groupName === GROUPS.KIGO ? 5 : parseInt(groupName[2], 10)]
}

const excludePlaceholderItems = (
  items: TransferListItem[],
): TransferListItem[] => {
  return items.filter((item) => item.value !== 0)
}

const isSelectedAll = (
  groupItems: TransferListItem[],
  selectedValueSet: Set<number | string>,
): boolean => {
  const itemsExcludingPlaceholder = excludePlaceholderItems(groupItems)
  return (
    !itemsExcludingPlaceholder.length ||
    itemsExcludingPlaceholder.every((item) => selectedValueSet.has(item.value))
  )
}

const TransferListModal = ({
  isOpen,
  onClose,
  onRightListChange,
  menuItems = [],
  selectedList,
  onFilterChange,
}: TransferListModalProps) => {
  const [selectedItems, setSelectedItems] = useState<TransferListItem[]>([])
  const [initialSelectedItems, setInitialSelectedItems] = useState<
    TransferListItem[]
  >([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedGroup, setSelectedGroup] = useState<ItemGroupName>(GROUPS.KIGO)
  const [initialSelectedGroup, setInitialSelectedGroup] =
    useState<ItemGroupName>(GROUPS.KIGO)
  const [isModified, setIsModified] = useState(false)
  const [isGroupDataLoading, setIsGroupDataLoading] = useState(false)
  const [isInitialLoading, setIsInitialLoading] = useState(true)
  const [loadingError, setLoadingError] = useState<string | null>(null)
  const [groupStates, setGroupStates] = useState<InMemoryGroupStates>({})
  const [lastSubmittedFilters, setLastSubmittedFilters] =
    useState<SubmittedFilterState | null>(null)
  const [hasRestoredGroup, setHasRestoredGroup] = useState(false)

  const selectedItemValueSet = useMemo(
    () => new Set(selectedItems.map((item) => item.value)),
    [selectedItems],
  )

  const initialSelectedValueSet = useMemo(
    () => new Set(initialSelectedItems.map((item) => item.value)),
    [initialSelectedItems],
  )

  const availableItems = useMemo(
    () => excludePlaceholderItems(menuItems),
    [menuItems],
  )

  const groupedItems = useMemo(() => {
    const groups = GROUP_NAMES.reduce(
      (acc, name) => ({ ...acc, [name]: [] }),
      {} as GroupedItemsMap,
    )

    menuItems.forEach((item) => {
      if (item.value === 0) {
        GROUP_NAMES.forEach((name) => groups[name].push(item))
      } else {
        groups[getItemGroupName(item.type)].push(item)
      }
    })

    GROUP_NAMES.forEach((groupName) => {
      const savedState = groupStates[groupName]
      if (savedState && savedState.allItems.length) {
        groups[groupName] = savedState.allItems
      }
    })

    return groups
  }, [menuItems, groupStates])

  const currentGroupItems = useMemo(
    () => groupedItems[selectedGroup],
    [groupedItems, selectedGroup],
  )

  const currentGroupAvailableItems = useMemo(
    () => excludePlaceholderItems(currentGroupItems),
    [currentGroupItems],
  )

  const isLoading = isInitialLoading || isGroupDataLoading

  const isAllSelectedInCurrentGroup = useMemo(
    () => isSelectedAll(currentGroupItems, selectedItemValueSet),
    [currentGroupItems, selectedItemValueSet],
  )

  const shouldShowSelectAll = useMemo(() => {
    return !isLoading && currentGroupAvailableItems.length > 0
  }, [isLoading, currentGroupAvailableItems.length])

  const handleClose = useCallback(async () => {
    setSelectedItems(initialSelectedItems)
    setSearchTerm('')
    onRightListChange?.(initialSelectedItems.length)

    if (onFilterChange && lastSubmittedFilters) {
      try {
        await Promise.all([
          onFilterChange(FILTER.filterCD, lastSubmittedFilters.filterCD),
          onFilterChange(FILTER.filterName, lastSubmittedFilters.filterName),
          onFilterChange(FILTER.filterType, lastSubmittedFilters.filterType),
        ])
      } catch (error) {
        console.error('Error restoring filter state:', error)
      }
    }

    onClose()
  }, [
    initialSelectedItems,
    onRightListChange,
    onClose,
    onFilterChange,
    lastSubmittedFilters,
  ])

  const handleSelect = useCallback(
    (id: number | string) => {
      if (id === 0) {
        const newList =
          isAllSelectedInCurrentGroup ?
            selectedItems.filter(
              (item) =>
                !currentGroupAvailableItems.some(
                  (groupItem) => groupItem.value === item.value,
                ),
            )
          : [
              ...selectedItems,
              ...currentGroupAvailableItems.filter(
                (item) => !selectedItemValueSet.has(item.value),
              ),
            ]
        setSelectedItems(newList)
        return
      }

      const isItemSelected = selectedItemValueSet.has(id)
      const targetItem = menuItems.find((item) => item.value === id)

      setSelectedItems(
        isItemSelected ? selectedItems.filter((item) => item.value !== id)
        : targetItem ? [...selectedItems, targetItem]
        : selectedItems,
      )
    },
    [
      currentGroupAvailableItems,
      isAllSelectedInCurrentGroup,
      selectedItems,
      selectedItemValueSet,
      menuItems,
    ],
  )

  const submitData = useCallback(async () => {
    const currentGroupSelectedItems = selectedItems.filter(
      (item) => getItemGroupName(item.type) === selectedGroup,
    )

    onRightListChange?.(currentGroupSelectedItems.length)

    if (onFilterChange) {
      try {
        const { filterCD: filterCDValue, filterName: filterNameValue } =
          calculateFilterValues(
            currentGroupSelectedItems,
            isAllSelectedInCurrentGroup,
          )

        await Promise.all([
          onFilterChange(FILTER.filterCD, filterCDValue),
          onFilterChange(FILTER.filterName, filterNameValue),
        ])

        const filterTypeValue = calculateFilterType(selectedGroup)

        setLastSubmittedFilters({
          filterCD: filterCDValue,
          filterName: filterNameValue,
          filterType: filterTypeValue,
          selectedGroup: selectedGroup,
        })
      } catch (error) {
        console.error('Error applying filter changes:', error)
      }
    }

    const currentGroupSelectedItemIds = selectedItems
      .filter((item) => getItemGroupName(item.type) === selectedGroup)
      .map((item) => item.value)

    setGroupStates((prev: InMemoryGroupStates) => ({
      ...prev,
      [selectedGroup]: {
        allItems: [...currentGroupItems],
        selectedItems: currentGroupSelectedItemIds,
        submittedAt: Date.now(),
      },
    }))

    setInitialSelectedItems([...selectedItems])
    setInitialSelectedGroup(selectedGroup)
    onClose()
  }, [
    selectedItems,
    isAllSelectedInCurrentGroup,
    onFilterChange,
    selectedGroup,
    onClose,
    onRightListChange,
    currentGroupItems,
  ])

  useEffect(() => {
    if (menuItems.length) {
      setIsInitialLoading(false)
      setIsGroupDataLoading(false)
      setLoadingError(null)
    } else if (isOpen) {
      setIsInitialLoading(true)
    }
  }, [menuItems, isOpen])

  useEffect(() => {
    if (isLoading) {
      const timeout = setTimeout(() => {
        if (menuItems.length === 0) {
          setLoadingError(
            'データの読み込みに時間がかかっています。しばらくお待ちください。',
          )
          setIsInitialLoading(false)
          setIsGroupDataLoading(false)
        }
      }, 30000)

      return () => clearTimeout(timeout)
    }
  }, [isLoading, menuItems.length])

  useEffect(() => {
    if (isOpen) {
      if (menuItems.length === 0) {
        setIsInitialLoading(true)
      }
      setIsGroupDataLoading(false)
    } else {
      setIsInitialLoading(true)
      setIsGroupDataLoading(false)
    }
  }, [isOpen, menuItems.length])

  useEffect(() => {
    if (
      isOpen &&
      selectedList &&
      menuItems.length &&
      lastSubmittedFilters === null
    ) {
      let filterCDValue: any
      let filterNameValue: any

      if (selectedList.length === 1 && selectedList[0] === 0) {
        filterCDValue = 0
        filterNameValue = 0
      } else {
        const selectedItems = menuItems.filter(
          (item) => selectedList.includes(item.value) && item.value !== 0,
        )

        const uniqueValues = selectedItems.reduce(
          (acc, item) => {
            acc.cd.add(item.cd)
            acc.name.add(item.name)
            return acc
          },
          { cd: new Set<string>(), name: new Set<string>() },
        )

        filterCDValue = Array.from(uniqueValues.cd)
        filterNameValue = Array.from(uniqueValues.name)
      }

      const filterTypeValue = [
        selectedGroup === GROUPS.KIGO ? 5 : selectedGroup[2],
      ]

      setLastSubmittedFilters({
        filterCD: filterCDValue,
        filterName: filterNameValue,
        filterType: filterTypeValue,
        selectedGroup: selectedGroup,
      })
    }
  }, [isOpen, selectedList, menuItems, selectedGroup, lastSubmittedFilters])

  useEffect(() => {
    if (!isOpen) {
      setHasRestoredGroup(false)
    }
  }, [isOpen])

  useEffect(() => {
    if (isOpen && lastSubmittedFilters && !hasRestoredGroup) {
      setSelectedGroup(lastSubmittedFilters.selectedGroup)
      setHasRestoredGroup(true)
    }
  }, [isOpen, lastSubmittedFilters, hasRestoredGroup])

  const handleSelectGroup = (groupName: ItemGroupName) => {
    if (selectedGroup === groupName) return

    const savedGroupState = groupStates[groupName]

    setSelectedGroup(groupName)
    setIsGroupDataLoading(true)

    onFilterChange?.(FILTER.filterName, 0)
    onFilterChange?.(FILTER.filterCD, 0)
    onFilterChange?.(FILTER.filterType, calculateFilterType(groupName))

    if (savedGroupState && savedGroupState.selectedItems.length) {
      const itemsToRestore = savedGroupState.allItems.filter(
        (item) =>
          savedGroupState.selectedItems.includes(item.value) &&
          item.value !== 0,
      )

      setSelectedItems((prev) => {
        const itemsFromOtherGroups = prev.filter(
          (item) => getItemGroupName(item.type) !== groupName,
        )
        return [...itemsFromOtherGroups, ...itemsToRestore]
      })
    }
  }

  useEffect(() => {
    if (!selectedList) return

    const savedGroupState = groupStates[selectedGroup]
    if (savedGroupState && savedGroupState.selectedItems.length) {
      const savedItems = savedGroupState.allItems.filter(
        (item) =>
          savedGroupState.selectedItems.includes(item.value) &&
          item.value !== 0,
      )

      const newItems =
        selectedList.length === 1 && selectedList[0] === 0 ?
          availableItems
        : menuItems.filter(
            (item) => selectedList.includes(item.value) && item.value !== 0,
          )

      const itemsFromOtherGroups = newItems.filter(
        (item) => getItemGroupName(item.type) !== selectedGroup,
      )

      const combinedItems = [...itemsFromOtherGroups, ...savedItems]
      setSelectedItems(combinedItems)
      setInitialSelectedItems(combinedItems)
    } else {
      const newItems =
        selectedList.length === 1 && selectedList[0] === 0 ?
          availableItems
        : menuItems.filter(
            (item) => selectedList.includes(item.value) && item.value !== 0,
          )

      setSelectedItems(newItems)
      setInitialSelectedItems(newItems)
    }
  }, [menuItems, selectedList, availableItems, selectedGroup, groupStates])

  const filteredItems = useMemo(() => {
    let items = currentGroupItems

    if (!shouldShowSelectAll) {
      items = items.filter((item) => item.value !== 0)
    }

    if (searchTerm) {
      items = items.filter((item) => item.label.includes(searchTerm))
    }

    return items
  }, [currentGroupItems, searchTerm, shouldShowSelectAll])

  const selectedItemsInCurrentGroup = useMemo(() => {
    return selectedItems.filter((item) => {
      if (item.value === 0) return false
      return getItemGroupName(item.type) === selectedGroup
    })
  }, [selectedItems, selectedGroup])

  const isItemSelected = useCallback(
    (id: number | string) => selectedItemValueSet.has(id),
    [selectedItemValueSet],
  )

  useEffect(() => {
    const hasListChanges =
      selectedItems.length !== initialSelectedItems.length ||
      selectedItems.some((item) => !initialSelectedValueSet.has(item.value)) ||
      initialSelectedItems.some((item) => !selectedItemValueSet.has(item.value))

    const hasGroupChanges = selectedGroup !== initialSelectedGroup
    setIsModified(hasListChanges || hasGroupChanges)
  }, [
    selectedItems,
    selectedGroup,
    initialSelectedItems,
    initialSelectedGroup,
    initialSelectedValueSet,
    selectedItemValueSet,
  ])

  useEffect(() => {
    if (!isOpen) return

    setSearchTerm('')
    setIsModified(false)

    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleClose()
      }
    }

    document.addEventListener('keydown', handleEscKey)
    return () => {
      document.removeEventListener('keydown', handleEscKey)
    }
  }, [isOpen, handleClose])

  const removeSelectedItem = useCallback((id: number | string) => {
    setSelectedItems((prev) => prev.filter((item) => item.value !== id))
  }, [])

  const isSidebarOpen = useMemo(() => localStorage.getItem('isSidebarOpen'), [])

  const SearchInput = () => (
    <div className={'rounded-t bg-blueGray-50 p-2'}>
      <input
        type={'text'}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className={'h-7 w-full rounded border p-2 focus:outline-none'}
        disabled={isLoading}
      />
    </div>
  )

  const EmptyState = () => (
    <div className={'flex h-[274px] items-center justify-center'}>
      <div className={'flex flex-col items-center gap-2'}>
        <span className={'text-sm text-gray-500'}>
          選択可能な項目がありません
        </span>
      </div>
    </div>
  )

  const UnifiedLoadingOverlay = () => {
    const loadingMessage =
      isInitialLoading ? 'データを読み込み中...' : 'グループデータを取得中...'

    return (
      <div
        className={
          'absolute inset-0 z-10 flex items-center justify-center rounded-sm bg-gray-50/80 backdrop-blur-sm'
        }
        role={'status'}
        aria-live={'assertive'}
        aria-label={`${loadingMessage} しばらくお待ちください`}
        aria-describedby={'loading-description'}
      >
        <div className={'flex flex-col items-center gap-4 p-8'}>
          <div
            className={
              'size-12 animate-spin rounded-full border-4 border-gray-300 border-t-green-600'
            }
            aria-hidden={'true'}
          />
        </div>
      </div>
    )
  }

  const ErrorState = ({ message }: { message: string }) => (
    <div
      className={'flex h-[274px] items-center justify-center'}
      role={'alert'}
      aria-live={'assertive'}
    >
      <div className={'flex flex-col items-center gap-3'}>
        <div className={'size-8 rounded-full bg-red-100 p-2'}>
          <div className={'size-4 text-red-600'}>⚠</div>
        </div>
        <span className={'text-sm text-red-600'}>{message}</span>
        <button
          onClick={() => setLoadingError(null)}
          className={
            'mt-2 rounded px-3 py-1 text-xs text-blue-600 hover:bg-blue-50'
          }
        >
          再試行
        </button>
      </div>
    </div>
  )

  const ItemCheckbox = ({
    item,
    isChecked,
  }: {
    item: TransferListItem
    isChecked: boolean
  }) => (
    <li className={'flex cursor-pointer items-center rounded pl-4'}>
      <label className={'flex cursor-pointer items-center'}>
        <input
          type={'checkbox'}
          checked={isChecked}
          onChange={() => handleSelect(item.value)}
          className={'mr-2 size-4 cursor-pointer accent-green-600'}
          disabled={isLoading}
        />
        {splitCodeAndNumber(item.label)[0]}
        {truncateText(splitCodeAndNumber(item.label)[1], 30)}
      </label>
    </li>
  )

  const SelectedItemTag = ({
    item,
    isSaved,
  }: {
    item: TransferListItem
    isSaved: boolean
  }) => (
    <div
      className={clsx(
        'my-1 mr-2 flex items-center rounded-full border border-gray-600 px-2 py-1 text-sm',
        'shrink-0',
        { 'text-gray-900': isSaved },
        { 'text-gray-600': !isSaved },
      )}
      style={{
        width: '120px',
        height: '28px',
        minWidth: '120px',
        minHeight: '28px',
        maxWidth: '120px',
        maxHeight: '28px',
      }}
    >
      <span className={'mr-2 min-w-0 flex-1'} title={item.label}>
        {splitCodeAndNumber(item.label)[0]}
        {truncateText(splitCodeAndNumber(item.label)[1], 6)}
      </span>
      <button
        onClick={() => removeSelectedItem(item.value)}
        className={'ml-2 shrink-0 text-xl text-gray-600'}
      >
        <CloseIcon fill={variables.gray600} />
      </button>
    </div>
  )

  if (!isOpen) {
    return null
  }

  return (
    <>
      <div
        className={clsx(
          'fixed inset-0 z-[9999] flex items-center bg-black/30',
          { 'justify-end': isSidebarOpen },
          { 'justify-center': !isSidebarOpen },
        )}
        onClick={handleClose}
        aria-label={'Close modal'}
      >
        <div
          className={clsx(
            'relative flex h-[560px] w-[960px] flex-col rounded-xl bg-white p-8 shadow-lg transition-all',
            { 'mr-[8%] 2xl:mr-[18%]': isSidebarOpen },
          )}
          onClick={(e) => e.stopPropagation()}
          role={'dialog'}
          aria-modal={'true'}
          aria-labelledby={'modal-title'}
          aria-describedby={'modal-description'}
          aria-busy={isLoading}
        >
          <div className={'absolute right-8 top-4 z-20'}>
            <button
              onClick={handleClose}
              className={' text-gray-700 hover:text-red-500'}
            >
              <CancelIcon fill={variables.gray600} />
            </button>
          </div>

          <div id={'modal-title'} className={'mb-3 mt-8 text-left text-base'}>
            {TITLE.transferListModal.title}
          </div>

          <div className={'mb-[6px] flex items-center space-x-6 pb-[8px]'}>
            {GROUP_NAMES.map((groupName) => (
              <RadioCustom
                key={groupName}
                size={'medium'}
                label={groupName}
                checked={selectedGroup === groupName}
                onChange={() => handleSelectGroup(groupName)}
                disabled={isLoading}
              />
            ))}
          </div>

          <div className={'relative'}>
            <div className={'flex gap-12'}>
              <div className={'flex h-80 w-1/2 flex-col rounded border'}>
                <SearchInput />
                <div className={'h-[274px]'}>
                  {loadingError ?
                    <ErrorState message={loadingError} />
                  : !filteredItems.length ?
                    <EmptyState />
                  : <FixedSizeList
                      className={'overflow-y-auto'}
                      height={274}
                      width={'100%'}
                      itemCount={filteredItems.length}
                      itemSize={40}
                      overscanCount={5}
                    >
                      {({ index, style }: { index: number; style: any }) => {
                        const item = filteredItems[index]
                        const isChecked =
                          item.value === 0 ?
                            isAllSelectedInCurrentGroup
                          : isItemSelected(item.value)
                        return (
                          <div style={style} className={'pt-1'}>
                            <ItemCheckbox item={item} isChecked={isChecked} />
                          </div>
                        )
                      }}
                    </FixedSizeList>
                  }
                </div>
              </div>

              <div
                className={
                  'h-[320px] w-1/2 overflow-y-auto rounded border pl-2'
                }
              >
                <div className={'h-full'}>
                  <FixedSizeList
                    className={'overflow-y-auto'}
                    height={318}
                    width={'100%'}
                    itemCount={Math.ceil(
                      selectedItemsInCurrentGroup.length / 3,
                    )}
                    itemSize={40}
                    overscanCount={5}
                  >
                    {({ index, style }: { index: number; style: any }) => {
                      const startIdx = index * 3
                      const rowItems = selectedItemsInCurrentGroup.slice(
                        startIdx,
                        startIdx + 3,
                      )

                      return (
                        <div style={style}>
                          <div
                            className={
                              'flex w-full flex-wrap overflow-hidden pr-2'
                            }
                          >
                            {rowItems.map((item) => {
                              const isSaved = initialSelectedItems.some(
                                (initialItem) =>
                                  initialItem.value === item.value,
                              )
                              return (
                                <SelectedItemTag
                                  key={item.value}
                                  item={item}
                                  isSaved={isSaved}
                                />
                              )
                            })}
                          </div>
                        </div>
                      )
                    }}
                  </FixedSizeList>
                </div>
              </div>
            </div>
            {isLoading && <UnifiedLoadingOverlay />}
          </div>

          <div className={'relative z-20 mt-8 flex justify-center gap-8'}>
            <button
              onClick={handleClose}
              className={
                'rounded-xl border-2 border-green-600 px-4 py-1 text-base-bold text-green-600 hover:border-green-700 hover:text-green-700'
              }
            >
              {TITLE.button.cancel}
            </button>
            <button
              disabled={!isModified || isLoading}
              onClick={submitData}
              className={clsx(
                'rounded-xl px-4 py-2 text-base-bold text-white transition-all',
                {
                  'bg-green-600 hover:bg-green-700':
                    isModified && !isGroupDataLoading,
                },
                {
                  'bg-gray-300 cursor-not-allowed':
                    !isModified || isGroupDataLoading,
                },
              )}
            >
              {TITLE.button.submit}
            </button>
          </div>
        </div>
      </div>
    </>
  )
}

export default TransferListModal
